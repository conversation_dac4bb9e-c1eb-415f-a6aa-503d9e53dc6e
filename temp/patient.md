# CliniCore (CC) Patient to AutoPatient (AP) Contact Synchronization Analysis

## Overview

This document provides a comprehensive analysis of the patient-contact synchronization system between CliniCore (CC) and AutoPatient (AP) platforms, focusing on the modernization from Socket.io + Bull/Redis queues to HTTP webhooks with synchronous processing.

## Architecture Overview

### Old Architecture (Socket.io + Bull/Redis)

- **Real-time Events**: Socket.io connection to CC platform
- **Queue Processing**: Bull/Redis for background job processing
- **Bidirectional Sync**: CC → AP and AP → CC data flow
- **Duplicate Prevention**: Skip logic using database tracking

### New Architecture (HTTP Webhooks)

- **HTTP Webhooks**: Direct webhook endpoints replacing Socket.io
- **Synchronous Processing**: Immediate processing without queues
- **Static Token Auth**: Simplified authentication model
- **Database Tracking**: PostgreSQL with apUpdatedAt/ccUpdatedAt fields

## Data Flow Diagrams

### CC → AP Patient Sync Flow

```
CC Platform → Webhook → ProcessPatientCreate/Update → updateOrCreateContact → AP Platform
     ↓              ↓                    ↓                        ↓
Socket Event → HTTP POST → Contact.searchCreateOrUpdate → contactReq.upsert/update
     ↓              ↓                    ↓                        ↓
Bull Queue → Direct Call → Database Update → Custom Fields Sync
```

### AP → CC Contact Sync Flow

```
AP Platform → Webhook → ProcessApContactCreate/Update → searchPatient/updatePatientToCC → CC Platform
     ↓              ↓                    ↓                           ↓
HTTP POST → Direct Call → Contact.searchCreateOrUpdate → patientReq.create/update
     ↓              ↓                    ↓                           ↓
Controller → Sync Function → Database Update → Custom Fields Sync
```

## Type Definitions

### CC Patient Types

**GetCCPatientType** (`src/lib/cc/CCTypes.ts:35-65`)

```typescript
export type GetCCPatientType = {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  firstName: string;
  lastName: string;
  dob: string | null;
  ssn: string | null;
  flashMessage: string;
  active: boolean;
  phoneMobile: string;
  email: string;
  title: string | null;
  titleSuffix: string | null;
  healthInsurance: string | null;
  gender: string | null;
  addresses: CCPatientAddress[];
  categories: number[];
  customFields: number[];
  invoices: number[];
  payments: number[];
  files: number[];
  history: number[];
  appointments: number[];
  messages: CCPatientMessage[];
  medications: CCPatientMedication[];
  qrUrl: string;
  avatarUrl: string | null;
};
```

**PostCCPatientType** (`src/lib/cc/CCTypes.ts:67-78`)

```typescript
export type PostCCPatientType = {
  firstName?: string;
  lastName?: string;
  dob?: string | null;
  ssn?: string | null;
  active?: boolean;
  phoneMobile?: string;
  email?: string;
  gender?: string | null;
  addresses?: CCPatientAddress[];
  customFields?: number[];
};
```

### AP Contact Types

**GetAPContactType** (`src/lib/ap/APTypes.ts:1-70`)

```typescript
export type GetAPContactType = {
  id: string;
  locationId: string;
  email?: string;
  phone?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  emailLowerCase?: string;
  timezone?: string;
  companyName?: string;
  dnd?: boolean;
  dndSettings?: {
    /* DND configuration */
  };
  type?: string;
  source?: string;
  assignedTo?: string;
  address1?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  website?: string;
  tags?: string[];
  dateOfBirth?: string;
  dateAdded?: string;
  dateUpdated?: string;
  attachments?: string;
  ssn?: string;
  gender?: string;
  keyword?: string;
  firstNameLowerCase?: string;
  fullNameLowerCase?: string;
  lastNameLowerCase?: string;
  lastActivity?: string;
  customFields?: { id: string; value: string }[];
  businessId?: string;
  attributionSource?: AttributionSourceType;
  lastAttributionSource?: AttributionSourceType;
};
```

**PostAPContactType** (`src/lib/ap/APTypes.ts:71-91`)

```typescript
export type PostAPContactType = {
  email?: string | null;
  phone?: string | null;
  name?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  timezone?: string | null;
  dnd?: boolean;
  source?: string | null;
  assignedTo?: string | null;
  address1?: string | null;
  city?: string | null;
  state?: string | null;
  country?: string | null;
  postalCode?: string | null;
  tags?: string[];
  dateOfBirth?: string | null;
  ssn?: string | null;
  gender?: string | null;
  customFields?: { id: string; value: string | number }[];
};
```

## Database Schema

### Patient Table (`src/database/schema.ts:22-32`)

```typescript
export const patient = pgTable("patients", {
  ...commonColumns,
  apId: varchar("ap_id", { length: 255 }).notNull().unique(),
  ccId: integer("cc_id").notNull().unique(),
  email: varchar("email", { length: 255 }),
  phone: varchar("phone", { length: 255 }),
  apUpdatedAt: timestamp("ap_updated_at"), // AP platform update tracking
  ccUpdatedAt: timestamp("cc_updated_at"), // CC platform update tracking
  apData: jsonb("ap_data"), // Full AP contact data
  ccData: jsonb("cc_data"), // Full CC patient data
});
```

### Key Database Fields for Sync Tracking

- **apUpdatedAt**: Timestamp of last update from AP platform
- **ccUpdatedAt**: Timestamp of last update from CC platform
- **apData**: Complete AP contact object for reference
- **ccData**: Complete CC patient object for reference

## Core Synchronization Functions

### CC → AP Sync Functions

**ProcessPatientCreate** (`old/app/Jobs/ProcessPatientCreate.ts:20-44`)

```typescript
export default class ProcessPatientCreate implements JobContract {
  public key = "ProcessPatientCreate";

  public async handle(job: {
    data: { payload: GetCCPatientType; auth: number };
  }) {
    const { payload, auth } = job.data;
    await setAPIAuth(auth);

    // Check for existing contact
    let contact = await Contact.findBy("ccId", payload.id);
    if (contact && contact.apId) {
      if (await Skip.hasProcessPatientCreate(contact.apId)) {
        return `We did this creation recently, so dropping it.`;
      }
    }

    // Search/create/update contact
    contact = await Contact.searchCreateOrUpdate({
      ccId: payload.id,
      ccData: payload,
      source: "cc",
      email: payload.email,
      phone: payload.phoneMobile,
    });

    if (contact.apId) {
      cLog(`Patient already exists in AP, ID: ${contact.apId}`);
      return;
    }

    await updateOrCreateContact(contact);
  }
}
```

**updateOrCreateContact** (`old/app/helpers/ap.ts:241-285`)

```typescript
export const updateOrCreateContact = async (
  contact: Contact,
  syncCustomfields = true
): Promise<Contact> => {
  if (!contact.ccData) {
    throw new Error("Required CC data is missing while creating contact");
  }
  if (!contact.email && !contact.phone) {
    throw new Error("Invalid contact data, email and phone is missing.");
  }

  const payload: PostAPContactType = {
    email: contact.email,
    phone: contact.phone,
    firstName: contact.ccData.firstName,
    lastName: contact.ccData.lastName,
    tags: ["cc_api"],
    dateOfBirth: contact.ccData.dob,
  };

  let apContact: GetAPContactType;
  if (!contact.apId) {
    payload.source = "cc";
    payload.gender = contact.ccData.gender;
    apContact = await contactReq.upsert(payload);
  } else {
    payload.tags = [...(payload.tags ?? []), ...(contact.apData.tags ?? [])];
    apContact = await contactReq.update(contact.apId, payload);
  }

  if (apContact) {
    contact.apData = apContact;
    contact.apId = apContact.id;
    await contact.save();
  }

  if (syncCustomfields) {
    await syncCCtoAPCustomfields(contact);
  }
  syncInvoicePayments(contact);
  return await contact.refresh();
};
```

### AP → CC Sync Functions

**searchPatient** (`old/app/helpers/cc.ts:69-101`)

```typescript
export const searchPatient = async (contact: Contact) => {
  let patient;
  if (!contact.phone && !contact.email) {
    throw new Error("Invalid data: Email and phone is missing");
  }

  // Search by email first
  if (contact.email) {
    patient = await patientReq.search(contact.email);
  }

  // Search by phone if not found
  if (!patient && contact.phone) {
    patient = await patientReq.search(contact.phone);
  }

  if (!patient) {
    // Create new patient
    const payload: PostCCPatientType = {
      firstName: contact.apData.firstName,
      lastName: contact.apData.lastName,
      email: contact.email,
      phoneMobile: contact.phone,
    };
    if (contact.apId) {
      payload["customFields"] = await syncApToCcCustomfields(contact, true);
    }
    const patient = await patientReq.create(payload);
    contact.ccData = patient;
    contact.ccId = patient.id;
    await contact.save();
  } else {
    // Update existing patient reference
    contact.ccData = patient;
    contact.ccId = patient.id;
    await contact.save();
  }
  return await contact.refresh();
};
```

**updatePatientToCC** (`old/app/helpers/cc.ts:103-121`)

```typescript
export const updatePatientToCC = async (contact: Contact) => {
  if (!contact.ccId) {
    cLog(`Patient doesn't have CC ID, Creating it now.`);
    return await searchPatient(contact);
  }

  const payload: PostCCPatientType = {
    firstName: contact.apData.firstName,
    lastName: contact.apData.lastName,
    email: contact.email,
    phoneMobile: contact.phone,
  };
  if (contact.apId) {
    payload["customFields"] = await syncApToCcCustomfields(contact, true);
  }

  const patient = await patientReq.update(contact.ccId, payload);
  contact.ccData = patient;
  await contact.save();
  return contact.refresh();
};
```

## Custom Fields Synchronization

### CC → AP Custom Fields (`old/app/helpers/ap.ts:30-80`)

```typescript
export const syncCCtoAPCustomfields = async (
  contact: Contact,
  extraFields: any = null
) => {
  if (!contact.apId || !contact.ccId) {
    throw new Error("AP Contact ID or CC Patient ID is missing");
  }

  let ccNameValue = await getCCCustomfieldsLabelValue(contact);
  if (extraFields) {
    ccNameValue = { ...ccNameValue, ...extraFields };
  }

  // Add computed fields
  ccNameValue["Total Appointments"] = contact.ccData.appointments.length;
  ccNameValue["Patient ID"] = contact.ccData.id;

  const services: KeyValue[] = await individualServiceAppointmentCount(contact);
  const spends: KeyValue[] = await individualServiceSpends(contact);

  if (Object.keys(services).length > 0) {
    ccNameValue = { ...ccNameValue, ...services };
  }
  if (Object.keys(spends).length > 0) {
    ccNameValue = { ...ccNameValue, ...spends };
  }

  const apNameId = await getApCustomfieldsNameId();
  const payload: PostAPContactType = {
    customFields: [],
  };

  if (Object.keys(ccNameValue).length > 0) {
    for (const key in ccNameValue) {
      if (key in apNameId && payload.customFields) {
        payload.customFields.push({
          id: apNameId[key],
          value: ccNameValue[key],
        });
      } else {
        // Create new custom field if not exists
        const cfRes = await apCustomfield.create({
          name: key,
          dataType: "TEXT",
        });
        if (cfRes && cfRes.id) {
          payload.customFields?.push({
            id: cfRes.id,
            value: ccNameValue[key],
          });
        }
      }
    }
  }

  // Update contact with custom fields
  await contactReq.update(contact.apId, payload);
};
```

### Financial Data Custom Fields (`old/utils/apCustomfields.ts`)

```typescript
export const apInvoiceCustomfields: Record<string, string> = {
  LatestInvoicePDFURL: "Latest Invoice PDF URL",
  LastInvoiceGrossAmount: "Latest Gross Amount",
  LastInvoiceDiscount: "Latest Discount",
  LastInvoiceTotalAmount: "Latest Total Amount",
  LatestPaymentStatus: "Latest Payment Status",
  LastInvoiceProducts: "Latest Products",
  LastInvoiceDiagnosis: "Latest Diagnosis",
  LastInvoiceTreatedBy: "Latest Treated By",
};

export const apPaymentCustomfields: Record<string, string> = {
  LatestPaymentStatus: "Latest Payment Status",
  LatestAmountPaid: "Latest Amount Paid",
  LatestPaymentDate: "Latest Payment Date",
  LatestPaymentPDFURL: "Latest Payment PDF URL",
};

export const apDueAmountCustomfield: string = "Due amount";
export const apCreditAmountCustomfield: string = "Credit amount";
export const apTotalInvoiceAmountCustomfield: string = "Total Invoiced Amount";
export const apLTVCustomfield: string = "Life Time Value";
```

## API Request Handlers

### CC Patient API (`old/request/cc.ts:4-64`)

```typescript
export const patientReq = {
  create: async (data: PostCCPatientType): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients`,
      method: "POST",
      data: { patient: removeNullEmptyProperties(data) },
    });
    return r.patient;
  },

  update: async (
    id: number,
    data: PostCCPatientType
  ): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients/${id}`,
      method: "PUT",
      data: { patient: removeNullEmptyProperties(data) },
    });
    return r.patient;
  },

  get: async (id: number): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients/${id}`,
      method: "GET",
    });
    return r.patient;
  },

  search: async (emailOrPhoneOr: string): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients?search=${emailOrPhoneOr}`,
      method: "GET",
    });
    return (r.patients && r.patients.length) > 0 ? r.patients[0] : null;
  },

  customFields: async (ids: number[]): Promise<GetCCPatientCustomField[]> =>
    ccRequest({
      url: "/patientCustomFields?" + idsToQueryString(ids),
      method: "get",
    }).then((r: any) => r.patientCustomFields),
};
```

### AP Contact API (`old/request/ap.ts:5-46`)

```typescript
export const contactReq = {
  get: (id: string): Promise<GetAPContactType> =>
    apRequestV2({ url: "/contacts/" + id, method: "get" }).then(
      (r: any) => r.contact
    ),

  create: (data: PostAPContactType): Promise<GetAPContactType> => {
    const auth = getAPIAuth();
    return apRequestV2({
      url: "/contacts/",
      method: "post",
      data: { ...removeNullEmptyProperties(data), locationId: auth.location },
    }).then((r: any) => r.contact);
  },

  upsert: (data: PostAPContactType): Promise<GetAPContactType> => {
    const auth = getAPIAuth();
    return apRequestV2({
      url: "/contacts/upsert/",
      method: "post",
      data: { ...removeNullEmptyProperties(data), locationId: auth.location },
    }).then((r: any) => r.contact);
  },

  update: (id: string, data: PostAPContactType): Promise<GetAPContactType> =>
    apRequestV2({
      url: "/contacts/" + id,
      method: "put",
      data: removeNullEmptyProperties(data),
    }).then((r: any) => r.contact),

  delete: (id: string) =>
    apRequestV2({ url: `/contacts/${id}/`, method: "delete" }),
};
```

## Webhook Processing

### Old Socket.io Implementation (`old/start/Socket.ts:41-83`)

```typescript
socket.on(
  "mobimed:App\\Events\\EntityWasCreated",
  async (data: SocketEventType) => {
    switch (data.type) {
      case "patient":
        Bull.add("ProcessPatientCreate", {
          payload: data.payload,
          auth: auth.id,
        });
        break;
      case "invoice":
      case "payment":
        Bull.add("ProcessInvoicePayment", {
          payload: data.payload,
          auth: auth.id,
        });
        break;
      default:
        console.log("mobimed:App\\Events\\EntityWasCreated", data.type);
        break;
    }
  }
);

socket.on(
  "mobimed:App\\Events\\EntityWasUpdated",
  async (data: SocketEventType) => {
    switch (data.type) {
      case "patient":
        Bull.add("ProcessPatientUpdate", {
          payload: data.payload,
          auth: auth.id,
        });
        break;
      default:
        console.log("mobimed:App\\Events\\EntityWasUpdated", data.type);
        break;
    }
  }
);
```

### New Webhook Implementation (Planned)

**CC Webhook Endpoint**: `POST /webhooks/cc` (`src/index.ts:22`)

```typescript
// CliniCore webhook endpoint (replaces Socket.io)
app.post("/webhooks/cc", ccWebhookProcessor);
```

**Expected CC Webhook Format**:

```json
{
  "event": "EntityWasCreated",
  "model": "Patient",
  "id": 123,
  "payload": {
    "id": 123,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phoneMobile": "+**********"
    // ... full GetCCPatientType object
  }
}
```

**AP Webhook Endpoints**: `POST /webhooks/ap/appointment/creates`, `POST /webhooks/ap/appointment/updates` (`src/index.ts:25-26`)

## Contact Model and Search Logic

### Contact.searchCreateOrUpdate (`old/app/Models/Contact.ts:62-109`)

```typescript
public static async searchCreateOrUpdate(payload: {
  apId?: string
  ccId?: number
  email?: string
  phone?: string
  ccData?: GetCCPatientType
  apData?: GetAPContactType
  source?: 'ap' | 'cc'
}): Promise<Contact> {
  let contact = Contact.query()

  // Search by platform IDs first
  if (payload.apId) {
    contact = contact.where('apId', payload.apId)
  } else if (payload.ccId) {
    contact = contact.where('ccId', parseInt(payload.ccId + ''))
  } else {
    // Search by email/phone
    if (payload.email) {
      contact = contact.where('email', payload.email)
    }
    if (payload.phone) {
      contact = contact.where('phone', payload.phone)
    }
  }

  const found = await contact.first()
  if (found) {
    // Update existing contact
    if (payload.apId) found.apId = payload.apId
    if (payload.ccId) found.ccId = payload.ccId
    if (payload.ccData) found.ccData = payload.ccData
    if (payload.apData) found.apData = payload.apData
    if (payload.phone) found.phone = payload.phone
    if (payload.email) found.email = payload.email
    await found.save()
    return await found.refresh()
  } else {
    // Create new contact
    return await Contact.create(payload)
  }
}
```

## Duplicate Prevention and Skip Logic

### Skip Model (`old/app/Models/Skip.ts:18-52`)

```typescript
export default class Skip extends BaseModel {
  @column({ isPrimary: true })
  public id: number;

  @column() public action: string;
  @column({ columnName: "cc_id" }) public ccId: number;
  @column({ columnName: "ap_id" }) public apId: string;

  public static async hasProcessPatientUpdate(ccId: number) {
    const has = await this.query()
      .where("action", "ProcessPatientUpdate")
      .where("ccId", ccId)
      .first();
    if (has) {
      has.delete();
      return true;
    }
    return false;
  }

  public static async hasProcessPatientCreate(apId: string) {
    const has = await this.query()
      .where("action", "putProcessPatientCreate")
      .where("apId", apId)
      .first();
    if (has) {
      has.delete();
      return true;
    }
    return false;
  }
}
```

## Error Handling and Logging

### Error Handling Patterns

```typescript
// From ProcessPatientCreate.ts
try {
  await updateOrCreateContact(contact);
} catch (error) {
  slackLogger.error(
    `Unable to create/update contact to AP, CC Data: ${JSON.stringify(
      contact.ccData
    )}`
  );
  throw new Error("Unable to create/update contact to AP");
}

// From updateOrCreateContact function
if (!contact.ccData) {
  throw new Error("Required CC data is missing while creating contact");
}
if (!contact.email && !contact.phone) {
  throw new Error("Invalid contact data, email and phone is missing.");
}
```

### Logging Implementation

```typescript
// Console logging with context
cLog(`Patient already exists in AP, ID: ${contact.apId}`);
cLog(
  `Contact has been synced to ap, ID: ${contact.apId}, Will sync the customfields soon.`
);

// Slack error notifications
slackLogger.error(
  `Unable to create/update contact to AP, CC Data: ${JSON.stringify(
    contact.ccData
  )}`
);
```

## Business Logic and Validation Rules

### Patient-Contact Sync Rules

1. **Email/Phone Requirement**: Both CC patients and AP contacts must have either email or phone
2. **Duplicate Prevention**: Use Skip model to prevent processing same entity multiple times
3. **Platform ID Mapping**: Maintain bidirectional mapping between ccId and apId
4. **Data Precedence**: CC data takes precedence for patient information, AP data for contact preferences
5. **Custom Fields**: Automatically create missing custom fields in AP when syncing from CC
6. **Financial Data**: Sync invoice/payment data as custom fields (LTV, due amounts, etc.)

### Field Mapping Rules

```typescript
// CC Patient → AP Contact mapping
const payload: PostAPContactType = {
  email: contact.email, // Direct mapping
  phone: contact.phone, // Direct mapping
  firstName: contact.ccData.firstName, // CC → AP
  lastName: contact.ccData.lastName, // CC → AP
  tags: ["cc_api"], // Add source tag
  dateOfBirth: contact.ccData.dob, // CC → AP
  gender: contact.ccData.gender, // CC → AP (create only)
  source: "cc", // Set source platform
};
```

## Modernization Context

### Key Changes from Old to New Architecture

1. **Socket.io → HTTP Webhooks**

   - Replace real-time socket connections with HTTP webhook endpoints
   - Remove dependency on persistent connections
   - Simplify deployment and scaling

2. **Bull/Redis Queues → Synchronous Processing**

   - Remove Redis dependency and queue infrastructure
   - Process webhook events synchronously
   - Simplify error handling and debugging

3. **AdonisJS → Cloudflare Workers + Hono**

   - Migrate from full-stack framework to serverless
   - Use Hono for lightweight HTTP handling
   - Leverage Cloudflare Workers for global distribution

4. **Database Schema Preservation**
   - Keep existing PostgreSQL schema with apUpdatedAt/ccUpdatedAt fields
   - Maintain bidirectional sync tracking
   - Preserve data integrity during migration

### Migration Considerations

1. **Webhook Security**: Implement proper authentication for webhook endpoints
2. **Rate Limiting**: Handle high-volume webhook events without queues
3. **Error Recovery**: Implement retry logic for failed sync operations
4. **Data Consistency**: Ensure no data loss during migration
5. **Performance**: Optimize for synchronous processing without background jobs

## Implementation Status

### Current State (New Codebase)

- ✅ Database schema defined (`src/database/schema.ts`)
- ✅ Type definitions ported (`src/lib/cc/CCTypes.ts`, `src/lib/ap/APTypes.ts`)
- ✅ API request layers structured (`src/lib/cc/request/`, `src/lib/ap/requests/`)
- ❌ Webhook processors not implemented
- ❌ Core sync functions not ported
- ❌ Skip logic not implemented

### Next Steps for Implementation

1. Create webhook processor functions (`ccWebhookProcessor`, `apWebhookProcessor`)
2. Port core sync functions (`updateOrCreateContact`, `searchPatient`, etc.)
3. Implement duplicate prevention logic
4. Add comprehensive error handling and logging
5. Create tests for patient-contact synchronization flows
