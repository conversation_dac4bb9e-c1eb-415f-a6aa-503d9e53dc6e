import { dbSchema, getDb } from "@database";
import type { GetCCPatientType } from "@libCC/CCTypes";
import { CCPatientRequest } from "@libCC/request/patientRequest";
import { eq } from "drizzle-orm";
import { logger } from "@/utils";

export class PatientService {
	private request: CCPatientRequest;
	private query = getDb().query.patient;
	private db = getDb();
	private patient: typeof dbSchema.patient.$inferSelect | undefined;
	private log = logger;

	constructor() {
		this.request = new CCPatientRequest();
		this.query = getDb().query.patient;
		this.db = getDb();
	}

	async sync(patient: GetCCPatientType) {
		const localPatient = await this.upsertLocal(patient);
		if (await this.isInBuffer(patient)) {
			this.log.info("The patient record is in buffer zone, dropping it.");
			return localPatient;
		}
		return this;
	}

	async getPatientById(id: number): Promise<GetCCPatientType> {
		return this.request.get(id);
	}

	async searchPatient(
		searchTerm: string | string[],
	): Promise<GetCCPatientType | null> {
		if (Array.isArray(searchTerm)) {
			// Search with multiple terms and return the first match found
			for (const term of searchTerm) {
				const result = await this.request.search(term);
				if (result !== null) {
					return result; // Return first match found
				}
			}
			return null; // No matches found
		}
		return this.request.search(searchTerm);
	}

	async upsertLocal(patient: GetCCPatientType) {
		this.patient = await this.query.findFirst({
			where: eq(dbSchema.patient.ccId, patient.id),
		});

		if (this.patient) {
			return this.patient;
		}

		const create = await this.db
			.insert(dbSchema.patient)
			.values({
				ccId: patient.id,
				ccData: patient,
				ccUpdatedAt: new Date(patient.updatedAt),
			})
			.returning();

		this.patient = create[0];
		return this;
	}

	async isInBuffer(payload: GetCCPatientType): Promise<boolean> {
		const payloadUpdatedAt = payload.updatedAt;
		const localUpdatedAt = this.patient?.ccUpdatedAt;

		// If no local timestamp exists, not in buffer
		if (!localUpdatedAt) {
			return false;
		}

		// Convert payload timestamp to Date object
		const payloadDate = new Date(payloadUpdatedAt);
		const localDate = new Date(localUpdatedAt);

		// Calculate the absolute difference in milliseconds
		// This handles both cases: payload ahead or behind local timestamp
		const timeDifferenceMs = Math.abs(
			payloadDate.getTime() - localDate.getTime(),
		);

		// Convert 1 minute to milliseconds (60 * 1000 = 60000)
		const oneMinuteMs = 60 * 1000;

		// Return true if within 1 minute buffer (ahead OR behind), false if outside buffer
		return timeDifferenceMs <= oneMinuteMs;
	}
}
