import { BaseModel } from './BaseModel';
import { apCustomFields, ccCustomFields } from '../schema';
import { eq, like, ilike } from 'drizzle-orm';
import type { 
  InferSelectModel, 
  InferInsertModel 
} from 'drizzle-orm';
import type { 
  APGetCustomFieldType, 
  GetCCCustomField 
} from '@/lib';

export type ApCustomFieldSelect = InferSelectModel<typeof apCustomFields>;
export type ApCustomFieldInsert = InferInsertModel<typeof apCustomFields>;
export type CcCustomFieldSelect = InferSelectModel<typeof ccCustomFields>;
export type CcCustomFieldInsert = InferInsertModel<typeof ccCustomFields>;

/**
 * AP Custom Field Model
 */
export class ApCustomFieldModel extends BaseModel<typeof apCustomFields> {
  constructor() {
    super(apCustomFields);
  }

  /**
   * Find custom field by AP ID
   */
  async findByApId(apId: string): Promise<ApCustomFieldSelect | undefined> {
    return await this.findFirst({
      where: eq(apCustomFields.apId, apId)
    });
  }

  /**
   * Find custom field by name
   */
  async findByName(name: string): Promise<ApCustomFieldSelect | undefined> {
    return await this.findFirst({
      where: eq(apCustomFields.name, name)
    });
  }

  /**
   * Search custom fields by name
   */
  async searchByName(term: string): Promise<ApCustomFieldSelect[]> {
    return await this.findMany({
      where: ilike(apCustomFields.name, `%${term}%`),
      orderBy: this.orderBy('name', 'asc')
    });
  }

  /**
   * Create or update from AP data
   */
  async upsertFromAp(
    apData: APGetCustomFieldType,
    existingField?: ApCustomFieldSelect
  ): Promise<ApCustomFieldSelect> {
    const fieldData: ApCustomFieldInsert = {
      apId: apData.id,
      name: apData.name,
      config: apData,
      ...(existingField && { id: existingField.id })
    };

    if (existingField) {
      return await this.updateById(existingField.id, fieldData);
    } else {
      return await this.create(fieldData);
    }
  }

  /**
   * Get all field names
   */
  async getAllNames(): Promise<string[]> {
    const fields = await this.findAll();
    return fields.map(field => field.name);
  }

  /**
   * Find fields by type (from config)
   */
  async findByType(type: string): Promise<ApCustomFieldSelect[]> {
    const fields = await this.findAll();
    return fields.filter(field => 
      field.config && (field.config as any).type === type
    );
  }
}

/**
 * CC Custom Field Model
 */
export class CcCustomFieldModel extends BaseModel<typeof ccCustomFields> {
  constructor() {
    super(ccCustomFields);
  }

  /**
   * Find custom field by CC ID
   */
  async findByCcId(ccId: number): Promise<CcCustomFieldSelect | undefined> {
    return await this.findFirst({
      where: eq(ccCustomFields.ccId, ccId)
    });
  }

  /**
   * Find custom field by name
   */
  async findByName(name: string): Promise<CcCustomFieldSelect | undefined> {
    return await this.findFirst({
      where: eq(ccCustomFields.name, name)
    });
  }

  /**
   * Search custom fields by name
   */
  async searchByName(term: string): Promise<CcCustomFieldSelect[]> {
    return await this.findMany({
      where: ilike(ccCustomFields.name, `%${term}%`),
      orderBy: this.orderBy('name', 'asc')
    });
  }

  /**
   * Create or update from CC data
   */
  async upsertFromCc(
    ccData: GetCCCustomField,
    existingField?: CcCustomFieldSelect
  ): Promise<CcCustomFieldSelect> {
    const fieldData: CcCustomFieldInsert = {
      ccId: ccData.id,
      name: ccData.name,
      config: ccData,
      ...(existingField && { id: existingField.id })
    };

    if (existingField) {
      return await this.updateById(existingField.id, fieldData);
    } else {
      return await this.create(fieldData);
    }
  }

  /**
   * Get all field names
   */
  async getAllNames(): Promise<string[]> {
    const fields = await this.findAll();
    return fields.map(field => field.name);
  }

  /**
   * Find fields by type (from config)
   */
  async findByType(type: string): Promise<CcCustomFieldSelect[]> {
    const fields = await this.findAll();
    return fields.filter(field => 
      field.config && (field.config as any).type === type
    );
  }
}

/**
 * Custom Field Manager - Handles both AP and CC custom fields
 */
export class CustomFieldManager {
  public ap: ApCustomFieldModel;
  public cc: CcCustomFieldModel;

  constructor() {
    this.ap = new ApCustomFieldModel();
    this.cc = new CcCustomFieldModel();
  }

  /**
   * Sync custom fields between platforms
   */
  async syncFields(): Promise<{
    apFields: ApCustomFieldSelect[];
    ccFields: CcCustomFieldSelect[];
    mappings: Array<{ apField: ApCustomFieldSelect; ccField: CcCustomFieldSelect }>;
  }> {
    const [apFields, ccFields] = await Promise.all([
      this.ap.findAll(),
      this.cc.findAll()
    ]);

    // Find matching fields by name
    const mappings: Array<{ apField: ApCustomFieldSelect; ccField: CcCustomFieldSelect }> = [];
    
    for (const apField of apFields) {
      const matchingCcField = ccFields.find(ccField => 
        ccField.name.toLowerCase() === apField.name.toLowerCase()
      );
      
      if (matchingCcField) {
        mappings.push({ apField, ccField: matchingCcField });
      }
    }

    return { apFields, ccFields, mappings };
  }

  /**
   * Get field mapping by name
   */
  async getFieldMapping(fieldName: string): Promise<{
    apField?: ApCustomFieldSelect;
    ccField?: CcCustomFieldSelect;
  }> {
    const [apField, ccField] = await Promise.all([
      this.ap.findByName(fieldName),
      this.cc.findByName(fieldName)
    ]);

    return { apField, ccField };
  }

  /**
   * Get all unique field names across both platforms
   */
  async getAllUniqueFieldNames(): Promise<string[]> {
    const [apNames, ccNames] = await Promise.all([
      this.ap.getAllNames(),
      this.cc.getAllNames()
    ]);

    const uniqueNames = new Set([...apNames, ...ccNames]);
    return Array.from(uniqueNames).sort();
  }

  /**
   * Find unmapped fields (exist in one platform but not the other)
   */
  async findUnmappedFields(): Promise<{
    apOnly: ApCustomFieldSelect[];
    ccOnly: CcCustomFieldSelect[];
  }> {
    const [apFields, ccFields] = await Promise.all([
      this.ap.findAll(),
      this.cc.findAll()
    ]);

    const ccFieldNames = new Set(ccFields.map(f => f.name.toLowerCase()));
    const apFieldNames = new Set(apFields.map(f => f.name.toLowerCase()));

    const apOnly = apFields.filter(field => 
      !ccFieldNames.has(field.name.toLowerCase())
    );

    const ccOnly = ccFields.filter(field => 
      !apFieldNames.has(field.name.toLowerCase())
    );

    return { apOnly, ccOnly };
  }

  /**
   * Get field statistics
   */
  async getStats(): Promise<{
    totalApFields: number;
    totalCcFields: number;
    mappedFields: number;
    unmappedApFields: number;
    unmappedCcFields: number;
  }> {
    const [apCount, ccCount, unmapped] = await Promise.all([
      this.ap.count(),
      this.cc.count(),
      this.findUnmappedFields()
    ]);

    const mappedFields = Math.min(apCount, ccCount) - Math.max(unmapped.apOnly.length, unmapped.ccOnly.length);

    return {
      totalApFields: apCount,
      totalCcFields: ccCount,
      mappedFields: Math.max(0, mappedFields),
      unmappedApFields: unmapped.apOnly.length,
      unmappedCcFields: unmapped.ccOnly.length
    };
  }
}

// Export singleton instances
export const apCustomFieldModel = new ApCustomFieldModel();
export const ccCustomFieldModel = new CcCustomFieldModel();
export const customFieldManager = new CustomFieldManager();
