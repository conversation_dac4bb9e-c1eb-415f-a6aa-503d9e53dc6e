import { BaseModel } from './BaseModel';
import { appointment, patient } from '../schema';
import { eq, and, or, gte, lte, isNull, isNotNull } from 'drizzle-orm';
import type { 
  InferSelectModel, 
  InferInsertModel 
} from 'drizzle-orm';
import type { 
  GetAPAppointmentType, 
  GetCCAppointmentType 
} from '@/lib';

export type AppointmentSelect = InferSelectModel<typeof appointment>;
export type AppointmentInsert = InferInsertModel<typeof appointment>;

/**
 * Appointment Model - Laravel-like repository for appointment operations
 * 
 * Provides specialized methods for appointment management including:
 * - Finding appointments by AP/CC IDs
 * - Patient relationship handling
 * - Date range queries
 * - Sync status management
 */
export class AppointmentModel extends BaseModel<typeof appointment> {
  constructor() {
    super(appointment);
  }

  /**
   * Find appointment by AutoPatient ID
   */
  async findByApId(apId: string): Promise<AppointmentSelect | undefined> {
    return await this.findFirst({
      where: eq(appointment.apId, apId)
    });
  }

  /**
   * Find appointment by CliniCore ID
   */
  async findByCcId(ccId: number): Promise<AppointmentSelect | undefined> {
    return await this.findFirst({
      where: eq(appointment.ccId, ccId)
    });
  }

  /**
   * Find appointments by patient ID
   */
  async findByPatientId(patientId: string): Promise<AppointmentSelect[]> {
    return await this.findMany({
      where: eq(appointment.patientId, patientId),
      orderBy: this.orderBy('createdAt', 'desc')
    });
  }

  /**
   * Find appointments by patient AP ID
   */
  async findByPatientApId(apId: string): Promise<AppointmentSelect[]> {
    // This would require a join with patient table
    // For now, simplified approach
    const appointments = await this.findAll();
    return appointments.filter(apt => {
      // You would typically do this with a proper join
      return apt.patientId; // Placeholder logic
    });
  }

  /**
   * Find appointments by patient CC ID
   */
  async findByPatientCcId(ccId: number): Promise<AppointmentSelect[]> {
    // This would require a join with patient table
    // For now, simplified approach
    const appointments = await this.findAll();
    return appointments.filter(apt => {
      // You would typically do this with a proper join
      return apt.patientId; // Placeholder logic
    });
  }

  /**
   * Find appointments in date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<AppointmentSelect[]> {
    return await this.findMany({
      where: and(
        gte(appointment.createdAt, startDate),
        lte(appointment.createdAt, endDate)
      ),
      orderBy: this.orderBy('createdAt', 'asc')
    });
  }

  /**
   * Find appointments that need AP sync
   */
  async findNeedingApSync(): Promise<AppointmentSelect[]> {
    return await this.findMany({
      where: and(
        isNotNull(appointment.ccId),
        or(
          isNull(appointment.apId),
          gte(appointment.ccUpdatedAt, appointment.apUpdatedAt)
        )
      )
    });
  }

  /**
   * Find appointments that need CC sync
   */
  async findNeedingCcSync(): Promise<AppointmentSelect[]> {
    return await this.findMany({
      where: and(
        isNotNull(appointment.apId),
        or(
          isNull(appointment.ccId),
          gte(appointment.apUpdatedAt, appointment.ccUpdatedAt)
        )
      )
    });
  }

  /**
   * Create or update appointment with AP data
   */
  async upsertFromAp(
    apData: GetAPAppointmentType,
    patientId: string,
    existingAppointment?: AppointmentSelect
  ): Promise<AppointmentSelect> {
    const appointmentData: AppointmentInsert = {
      apId: apData.id,
      patientId: patientId,
      apData: apData,
      apUpdatedAt: new Date(),
      // Preserve existing CC data if updating
      ...(existingAppointment && {
        id: existingAppointment.id,
        ccId: existingAppointment.ccId,
        ccData: existingAppointment.ccData,
        ccUpdatedAt: existingAppointment.ccUpdatedAt,
        apNoteID: existingAppointment.apNoteID,
      })
    };

    if (existingAppointment) {
      return await this.updateById(existingAppointment.id, appointmentData);
    } else {
      return await this.create(appointmentData);
    }
  }

  /**
   * Create or update appointment with CC data
   */
  async upsertFromCc(
    ccData: GetCCAppointmentType,
    patientId: string,
    existingAppointment?: AppointmentSelect
  ): Promise<AppointmentSelect> {
    const appointmentData: AppointmentInsert = {
      ccId: ccData.id,
      patientId: patientId,
      ccData: ccData,
      ccUpdatedAt: new Date(),
      // Preserve existing AP data if updating
      ...(existingAppointment && {
        id: existingAppointment.id,
        apId: existingAppointment.apId,
        apData: existingAppointment.apData,
        apUpdatedAt: existingAppointment.apUpdatedAt,
        apNoteID: existingAppointment.apNoteID,
      })
    };

    if (existingAppointment) {
      return await this.updateById(existingAppointment.id, appointmentData);
    } else {
      return await this.create(appointmentData);
    }
  }

  /**
   * Update AP note ID for appointment
   */
  async updateApNoteId(appointmentId: string, noteId: string): Promise<AppointmentSelect | undefined> {
    return await this.updateById(appointmentId, { apNoteID: noteId });
  }

  /**
   * Find appointments with missing patient links
   */
  async findOrphaned(): Promise<AppointmentSelect[]> {
    return await this.findMany({
      where: isNull(appointment.patientId)
    });
  }

  /**
   * Find appointments by sync status
   */
  async findBySyncStatus(platform: 'ap' | 'cc', synced: boolean): Promise<AppointmentSelect[]> {
    if (platform === 'ap') {
      return await this.findMany({
        where: synced 
          ? isNotNull(appointment.apId)
          : isNull(appointment.apId)
      });
    } else {
      return await this.findMany({
        where: synced 
          ? isNotNull(appointment.ccId)
          : isNull(appointment.ccId)
      });
    }
  }

  /**
   * Get appointment statistics
   */
  async getStats(): Promise<{
    total: number;
    syncedToAp: number;
    syncedToCc: number;
    orphaned: number;
  }> {
    const [total, syncedToAp, syncedToCc, orphaned] = await Promise.all([
      this.count(),
      this.count(isNotNull(appointment.apId)),
      this.count(isNotNull(appointment.ccId)),
      this.count(isNull(appointment.patientId))
    ]);

    return {
      total,
      syncedToAp,
      syncedToCc,
      orphaned
    };
  }

  /**
   * Bulk update AP sync timestamps
   */
  async bulkUpdateApSync(appointmentIds: string[]): Promise<AppointmentSelect[]> {
    return await this.updateMany(
      this.where('id', 'in', appointmentIds),
      { apUpdatedAt: new Date() }
    );
  }

  /**
   * Bulk update CC sync timestamps
   */
  async bulkUpdateCcSync(appointmentIds: string[]): Promise<AppointmentSelect[]> {
    return await this.updateMany(
      this.where('id', 'in', appointmentIds),
      { ccUpdatedAt: new Date() }
    );
  }

  /**
   * Find recent appointments (last 30 days)
   */
  async findRecent(days: number = 30): Promise<AppointmentSelect[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return await this.findMany({
      where: gte(appointment.createdAt, cutoffDate),
      orderBy: this.orderBy('createdAt', 'desc')
    });
  }

  /**
   * Check if appointment is in sync buffer
   */
  async isInBuffer(appointmentId: string, bufferSeconds: number = 60): Promise<boolean> {
    const apt = await this.findById(appointmentId);
    if (!apt) return false;

    const now = new Date();
    const bufferTime = new Date(now.getTime() - (bufferSeconds * 1000));
    
    return apt.updatedAt > bufferTime;
  }
}

// Export singleton instance
export const appointmentModel = new AppointmentModel();
