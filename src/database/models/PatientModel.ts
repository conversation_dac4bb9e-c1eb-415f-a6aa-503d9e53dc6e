import type { InferInsertModel, InferSelectModel } from "drizzle-orm";
import { and, eq, ilike, like, or } from "drizzle-orm";
import type { GetAPContactType, GetCCPatientType } from "@/lib";
import { patient } from "../schema";
import { BaseModel } from "./BaseModel";

export type PatientSelect = InferSelectModel<typeof patient>;
export type PatientInsert = InferInsertModel<typeof patient>;

/**
 * Patient Model - Laravel-like repository for patient operations
 *
 * Provides specialized methods for patient management including:
 * - Finding patients by AP/CC IDs
 * - Email and phone search
 * - Sync status checking
 * - Platform-specific data handling
 */
export class PatientModel extends BaseModel<typeof patient> {
	constructor() {
		super(patient);
	}

	/**
	 * Find patient by AutoPatient ID
	 */
	async findByApId(apId: string): Promise<PatientSelect | undefined> {
		return await this.findFirst({
			where: eq(patient.apId, apId),
		});
	}

	/**
	 * Find patient by CliniCore ID
	 */
	async findByCcId(ccId: number): Promise<PatientSelect | undefined> {
		return await this.findFirst({
			where: eq(patient.ccId, ccId),
		});
	}

	/**
	 * Find patient by email
	 */
	async findByEmail(email: string): Promise<PatientSelect | undefined> {
		return await this.findFirst({
			where: eq(patient.email, email),
		});
	}

	/**
	 * Find patient by phone
	 */
	async findByPhone(phone: string): Promise<PatientSelect | undefined> {
		return await this.findFirst({
			where: eq(patient.phone, phone),
		});
	}

	/**
	 * Search patients by email or phone (fuzzy search)
	 */
	async search(term: string): Promise<PatientSelect[]> {
		return await this.findMany({
			where: or(
				ilike(patient.email, `%${term}%`),
				ilike(patient.phone, `%${term}%`),
			),
		});
	}

	/**
	 * Find patients that need AP sync (CC updated more recently)
	 */
	async findNeedingApSync(): Promise<PatientSelect[]> {
		return await this.findMany({
			where: and(
				eq(patient.ccUpdatedAt, patient.updatedAt),
				// Add buffer time logic here if needed
			),
		});
	}

	/**
	 * Find patients that need CC sync (AP updated more recently)
	 */
	async findNeedingCcSync(): Promise<PatientSelect[]> {
		return await this.findMany({
			where: and(
				eq(patient.apUpdatedAt, patient.updatedAt),
				// Add buffer time logic here if needed
			),
		});
	}

	/**
	 * Create or update patient with AP data
	 */
	async upsertFromAp(
		apData: GetAPContactType,
		existingPatient?: PatientSelect,
	): Promise<PatientSelect> {
		const patientData: PatientInsert = {
			apId: apData.id,
			email: apData.email,
			phone: apData.phone,
			apData: apData,
			apUpdatedAt: new Date(),
			// Preserve existing CC data if updating
			...(existingPatient && {
				id: existingPatient.id,
				ccId: existingPatient.ccId,
				ccData: existingPatient.ccData,
				ccUpdatedAt: existingPatient.ccUpdatedAt,
			}),
		};

		if (existingPatient) {
			return await this.updateById(existingPatient.id, patientData);
		} else {
			return await this.create(patientData);
		}
	}

	/**
	 * Create or update patient with CC data
	 */
	async upsertFromCc(
		ccData: GetCCPatientType,
		existingPatient?: PatientSelect,
	): Promise<PatientSelect> {
		const patientData: PatientInsert = {
			ccId: ccData.id,
			email: ccData.email,
			phone: ccData.phone,
			ccData: ccData,
			ccUpdatedAt: new Date(),
			// Preserve existing AP data if updating
			...(existingPatient && {
				id: existingPatient.id,
				apId: existingPatient.apId,
				apData: existingPatient.apData,
				apUpdatedAt: existingPatient.apUpdatedAt,
			}),
		};

		if (existingPatient) {
			return await this.updateById(existingPatient.id, patientData);
		} else {
			return await this.create(patientData);
		}
	}

	/**
	 * Check if patient is in sync buffer (recently updated)
	 */
	async isInBuffer(
		patientId: string,
		bufferSeconds: number = 60,
	): Promise<boolean> {
		const patient = await this.findById(patientId);
		if (!patient) return false;

		const now = new Date();
		const bufferTime = new Date(now.getTime() - bufferSeconds * 1000);

		return patient.updatedAt > bufferTime;
	}

	/**
	 * Get patients with their appointment count
	 */
	async findWithAppointmentCount(): Promise<
		Array<PatientSelect & { appointmentCount: number }>
	> {
		// This would require a join - simplified version
		const patients = await this.findAll();

		// You would typically do this with a proper join query
		// For now, returning patients with a placeholder count
		return patients.map((patient) => ({
			...patient,
			appointmentCount: 0, // This should be calculated via join
		}));
	}

	/**
	 * Find patients by platform sync status
	 */
	async findBySyncStatus(
		platform: "ap" | "cc",
		synced: boolean,
	): Promise<PatientSelect[]> {
		if (platform === "ap") {
			return await this.findMany({
				where: synced
					? eq(patient.apId, patient.apId) // Has AP ID
					: eq(patient.apId, null), // No AP ID
			});
		} else {
			return await this.findMany({
				where: synced
					? eq(patient.ccId, patient.ccId) // Has CC ID
					: eq(patient.ccId, null), // No CC ID
			});
		}
	}

	/**
	 * Bulk update AP sync timestamps
	 */
	async bulkUpdateApSync(patientIds: string[]): Promise<PatientSelect[]> {
		return await this.updateMany(this.where("id", "in", patientIds), {
			apUpdatedAt: new Date(),
		});
	}

	/**
	 * Bulk update CC sync timestamps
	 */
	async bulkUpdateCcSync(patientIds: string[]): Promise<PatientSelect[]> {
		return await this.updateMany(this.where("id", "in", patientIds), {
			ccUpdatedAt: new Date(),
		});
	}
}

// Export singleton instance
export const patientModel = new PatientModel();
