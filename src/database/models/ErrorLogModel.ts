import { BaseModel } from './BaseModel';
import { errorLogs } from '../schema';
import { eq, and, gte, lte, like, desc } from 'drizzle-orm';
import type { 
  InferSelectModel, 
  InferInsertModel 
} from 'drizzle-orm';

export type ErrorLogSelect = InferSelectModel<typeof errorLogs>;
export type ErrorLogInsert = InferInsertModel<typeof errorLogs>;

export interface ErrorLogData {
  message: string;
  stack?: string;
  type: string;
  data?: Record<string, any>;
}

/**
 * ErrorLog Model - Laravel-like repository for error logging
 * 
 * Provides specialized methods for error management including:
 * - Error type filtering
 * - Date range queries
 * - Error statistics
 * - Cleanup operations
 */
export class ErrorLogModel extends BaseModel<typeof errorLogs> {
  constructor() {
    super(errorLogs);
  }

  /**
   * Log an error
   */
  async logError(errorData: ErrorLogData): Promise<ErrorLogSelect> {
    return await this.create({
      message: errorData.message,
      stack: errorData.stack,
      type: errorData.type,
      data: errorData.data || null
    });
  }

  /**
   * Log a sync error
   */
  async logSyncError(
    operation: string,
    platform: 'ap' | 'cc',
    error: Error,
    additionalData?: Record<string, any>
  ): Promise<ErrorLogSelect> {
    return await this.logError({
      message: error.message,
      stack: error.stack,
      type: `sync_${platform}_${operation}`,
      data: {
        platform,
        operation,
        ...additionalData
      }
    });
  }

  /**
   * Log an API error
   */
  async logApiError(
    endpoint: string,
    method: string,
    statusCode: number,
    error: Error,
    additionalData?: Record<string, any>
  ): Promise<ErrorLogSelect> {
    return await this.logError({
      message: error.message,
      stack: error.stack,
      type: 'api_error',
      data: {
        endpoint,
        method,
        statusCode,
        ...additionalData
      }
    });
  }

  /**
   * Log a webhook error
   */
  async logWebhookError(
    platform: 'ap' | 'cc',
    webhookType: string,
    error: Error,
    payload?: any
  ): Promise<ErrorLogSelect> {
    return await this.logError({
      message: error.message,
      stack: error.stack,
      type: `webhook_${platform}_${webhookType}`,
      data: {
        platform,
        webhookType,
        payload
      }
    });
  }

  /**
   * Find errors by type
   */
  async findByType(type: string): Promise<ErrorLogSelect[]> {
    return await this.findMany({
      where: eq(errorLogs.type, type),
      orderBy: this.orderBy('createdAt', 'desc')
    });
  }

  /**
   * Find errors by type pattern
   */
  async findByTypePattern(pattern: string): Promise<ErrorLogSelect[]> {
    return await this.findMany({
      where: like(errorLogs.type, `%${pattern}%`),
      orderBy: this.orderBy('createdAt', 'desc')
    });
  }

  /**
   * Find errors in date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<ErrorLogSelect[]> {
    return await this.findMany({
      where: and(
        gte(errorLogs.createdAt, startDate),
        lte(errorLogs.createdAt, endDate)
      ),
      orderBy: this.orderBy('createdAt', 'desc')
    });
  }

  /**
   * Find recent errors
   */
  async findRecent(hours: number = 24): Promise<ErrorLogSelect[]> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);

    return await this.findMany({
      where: gte(errorLogs.createdAt, cutoffDate),
      orderBy: this.orderBy('createdAt', 'desc')
    });
  }

  /**
   * Find sync errors
   */
  async findSyncErrors(platform?: 'ap' | 'cc'): Promise<ErrorLogSelect[]> {
    const pattern = platform ? `sync_${platform}_%` : 'sync_%';
    return await this.findByTypePattern(pattern);
  }

  /**
   * Find API errors
   */
  async findApiErrors(): Promise<ErrorLogSelect[]> {
    return await this.findByType('api_error');
  }

  /**
   * Find webhook errors
   */
  async findWebhookErrors(platform?: 'ap' | 'cc'): Promise<ErrorLogSelect[]> {
    const pattern = platform ? `webhook_${platform}_%` : 'webhook_%';
    return await this.findByTypePattern(pattern);
  }

  /**
   * Get error statistics
   */
  async getStats(hours: number = 24): Promise<{
    total: number;
    syncErrors: number;
    apiErrors: number;
    webhookErrors: number;
    byType: Record<string, number>;
  }> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);

    const recentErrors = await this.findByDateRange(cutoffDate, new Date());

    const stats = {
      total: recentErrors.length,
      syncErrors: 0,
      apiErrors: 0,
      webhookErrors: 0,
      byType: {} as Record<string, number>
    };

    for (const error of recentErrors) {
      // Count by category
      if (error.type.startsWith('sync_')) {
        stats.syncErrors++;
      } else if (error.type === 'api_error') {
        stats.apiErrors++;
      } else if (error.type.startsWith('webhook_')) {
        stats.webhookErrors++;
      }

      // Count by type
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    }

    return stats;
  }

  /**
   * Get error frequency (errors per hour)
   */
  async getErrorFrequency(hours: number = 24): Promise<Array<{ hour: string; count: number }>> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);

    const errors = await this.findByDateRange(cutoffDate, new Date());

    // Group by hour
    const hourlyStats: Record<string, number> = {};
    
    for (const error of errors) {
      const hour = error.createdAt.toISOString().slice(0, 13); // YYYY-MM-DDTHH
      hourlyStats[hour] = (hourlyStats[hour] || 0) + 1;
    }

    return Object.entries(hourlyStats).map(([hour, count]) => ({
      hour,
      count
    })).sort((a, b) => a.hour.localeCompare(b.hour));
  }

  /**
   * Clean up old errors
   */
  async cleanup(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const deletedErrors = await this.deleteMany(
      this.where('createdAt', '<', cutoffDate)
    );

    return deletedErrors.length;
  }

  /**
   * Find errors with similar messages
   */
  async findSimilar(message: string, limit: number = 10): Promise<ErrorLogSelect[]> {
    return await this.findMany({
      where: like(errorLogs.message, `%${message}%`),
      orderBy: this.orderBy('createdAt', 'desc'),
      limit
    });
  }

  /**
   * Get most common error types
   */
  async getMostCommonTypes(limit: number = 10): Promise<Array<{ type: string; count: number }>> {
    // This would typically be done with a GROUP BY query
    // For now, simplified approach
    const errors = await this.findAll();
    
    const typeCounts: Record<string, number> = {};
    for (const error of errors) {
      typeCounts[error.type] = (typeCounts[error.type] || 0) + 1;
    }

    return Object.entries(typeCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * Check if error rate is above threshold
   */
  async isErrorRateHigh(
    thresholdPerHour: number = 10,
    checkHours: number = 1
  ): Promise<boolean> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - checkHours);

    const recentErrorCount = await this.count(
      gte(errorLogs.createdAt, cutoffDate)
    );

    return recentErrorCount > (thresholdPerHour * checkHours);
  }
}

// Export singleton instance
export const errorLogModel = new ErrorLogModel();
