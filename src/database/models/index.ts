/**
 * Database Models Index
 * 
 * Exports all database models and their types for easy importing
 * throughout the application.
 */

// Base Model
export { BaseModel } from './BaseModel';
export type {
  WhereCondition,
  OrderByCondition,
  QueryOptions,
  PaginationOptions,
  PaginatedResult
} from './BaseModel';

// Patient Model
export { 
  PatientModel, 
  patientModel 
} from './PatientModel';
export type {
  PatientSelect,
  PatientInsert
} from './PatientModel';

// Appointment Model
export { 
  AppointmentModel, 
  appointmentModel 
} from './AppointmentModel';
export type {
  AppointmentSelect,
  AppointmentInsert
} from './AppointmentModel';

// Error Log Model
export { 
  ErrorLogModel, 
  errorLogModel 
} from './ErrorLogModel';
export type {
  ErrorLogSelect,
  ErrorLogInsert,
  ErrorLogData
} from './ErrorLogModel';

// Custom Field Models
export { 
  ApCustomFieldModel,
  CcCustomFieldModel,
  CustomFieldManager,
  apCustomFieldModel,
  ccCustomFieldModel,
  customFieldManager
} from './CustomFieldModel';
export type {
  ApCustomFieldSelect,
  ApCustomFieldInsert,
  CcCustomFieldSelect,
  CcCustomFieldInsert
} from './CustomFieldModel';

// Convenience exports for commonly used models
export const models = {
  patient: patientModel,
  appointment: appointmentModel,
  errorLog: errorLogModel,
  customFields: customFieldManager
} as const;

// Type helpers for model instances
export type ModelInstance<T> = T extends BaseModel<infer U> ? U : never;

/**
 * Database Model Factory
 * 
 * Provides a centralized way to access all models
 */
export class ModelFactory {
  static get patient() {
    return patientModel;
  }

  static get appointment() {
    return appointmentModel;
  }

  static get errorLog() {
    return errorLogModel;
  }

  static get customFields() {
    return customFieldManager;
  }

  static get apCustomFields() {
    return apCustomFieldModel;
  }

  static get ccCustomFields() {
    return ccCustomFieldModel;
  }

  /**
   * Get all models
   */
  static getAll() {
    return {
      patient: this.patient,
      appointment: this.appointment,
      errorLog: this.errorLog,
      customFields: this.customFields,
      apCustomFields: this.apCustomFields,
      ccCustomFields: this.ccCustomFields
    };
  }

  /**
   * Health check for all models
   */
  static async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    models: Record<string, { status: 'ok' | 'error'; error?: string }>;
  }> {
    const results: Record<string, { status: 'ok' | 'error'; error?: string }> = {};
    let overallStatus: 'healthy' | 'unhealthy' = 'healthy';

    const modelChecks = [
      { name: 'patient', model: this.patient },
      { name: 'appointment', model: this.appointment },
      { name: 'errorLog', model: this.errorLog },
      { name: 'apCustomFields', model: this.apCustomFields },
      { name: 'ccCustomFields', model: this.ccCustomFields }
    ];

    for (const { name, model } of modelChecks) {
      try {
        await model.count();
        results[name] = { status: 'ok' };
      } catch (error) {
        results[name] = { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
        overallStatus = 'unhealthy';
      }
    }

    return { status: overallStatus, models: results };
  }

  /**
   * Get database statistics
   */
  static async getStats(): Promise<{
    patients: number;
    appointments: number;
    errorLogs: number;
    apCustomFields: number;
    ccCustomFields: number;
  }> {
    const [
      patients,
      appointments,
      errorLogs,
      apCustomFields,
      ccCustomFields
    ] = await Promise.all([
      this.patient.count(),
      this.appointment.count(),
      this.errorLog.count(),
      this.apCustomFields.count(),
      this.ccCustomFields.count()
    ]);

    return {
      patients,
      appointments,
      errorLogs,
      apCustomFields,
      ccCustomFields
    };
  }
}

// Export the factory as default
export default ModelFactory;
