// base-repository.ts
import { db } from "./db";
import { eq } from "drizzle-orm";
import type { Table, InferModel } from "drizzle-orm";

export abstract class BaseRepository<TTable extends Table> {
  protected table: TTable;

  constructor(table: TTable) {
    this.table = table;
  }

  async findById(id: number) {
    // @ts-ignore - assume all tables have `id`
    const rows = await db
      .select()
      .from(this.table)
      .where(eq(this.table.id, id));
    return rows[0];
  }

  async findAll() {
    return db.select().from(this.table);
  }

  async create(data: InferModel<TTable, "insert">) {
    const [created] = await db.insert(this.table).values(data).returning();
    return created;
  }

  async update(id: number, data: Partial<InferModel<TTable, "insert">>) {
    // @ts-ignore
    const [updated] = await db
      .update(this.table)
      .set(data)
      .where(eq(this.table.id, id))
      .returning();
    return updated;
  }

  async delete(id: number) {
    // @ts-ignore
    const [deleted] = await db
      .delete(this.table)
      .where(eq(this.table.id, id))
      .returning();
    return deleted;
  }

  async refresh(id: number) {
    return this.findById(id);
  }
}
