import type {
	Column,
	InferInsertModel,
	InferSelectModel,
	Table,
} from "drizzle-orm";
import {
	and,
	asc,
	count,
	desc,
	eq,
	gt,
	gte,
	ilike,
	inArray,
	isNotNull,
	isNull,
	like,
	lt,
	lte,
	ne,
	notInArray,
	or,
	type SQL,
	sql,
} from "drizzle-orm";
import { getDb } from "@/database";

// Types for query building
export type WhereCondition<TTable extends Table> = SQL<unknown> | undefined;
export type OrderByCondition<TTable extends Table> =
	| SQL<unknown>
	| Column
	| SQL<unknown>[];

export interface QueryOptions<TTable extends Table> {
	where?: WhereCondition<TTable>;
	orderBy?: OrderByCondition<TTable>;
	limit?: number;
	offset?: number;
	select?: Partial<Record<keyof InferSelectModel<TTable>, boolean>>;
}

export interface PaginationOptions {
	page?: number;
	perPage?: number;
}

export interface PaginatedResult<T> {
	data: T[];
	pagination: {
		page: number;
		perPage: number;
		total: number;
		totalPages: number;
		hasNext: boolean;
		hasPrev: boolean;
	};
}

/**
 * Base Model class providing Laravel-like repository pattern for Drizzle ORM
 *
 * Features:
 * - CRUD operations with proper typing
 * - Query builder methods
 * - Pagination support
 * - Soft deletes (if table has deletedAt column)
 * - Timestamps handling
 * - Relationship loading
 * - Bulk operations
 * - Transaction support
 */
export abstract class BaseModel<TTable extends Table> {
	protected table: TTable;
	protected db = getDb();

	constructor(table: TTable) {
		this.table = table;
	}

	/**
	 * Find a record by ID
	 */
	async findById(id: string): Promise<InferSelectModel<TTable> | undefined> {
		const rows = await this.db
			.select()
			.from(this.table)
			.where(eq(this.table.id, id))
			.limit(1);
		return rows[0];
	}

	/**
	 * Find a record by ID or throw error
	 */
	async findByIdOrFail(id: string): Promise<InferSelectModel<TTable>> {
		const record = await this.findById(id);
		if (!record) {
			throw new Error(`Record with ID ${id} not found`);
		}
		return record;
	}

	/**
	 * Find first record matching conditions
	 */
	async findFirst(
		options?: QueryOptions<TTable>,
	): Promise<InferSelectModel<TTable> | undefined> {
		let query = this.db.select().from(this.table);

		if (options?.where) {
			query = query.where(options.where);
		}

		if (options?.orderBy) {
			query = query.orderBy(options.orderBy);
		}

		const rows = await query.limit(1);
		return rows[0];
	}

	/**
	 * Find first record or throw error
	 */
	async findFirstOrFail(
		options?: QueryOptions<TTable>,
	): Promise<InferSelectModel<TTable>> {
		const record = await this.findFirst(options);
		if (!record) {
			throw new Error("No record found matching the criteria");
		}
		return record;
	}

	/**
	 * Find all records matching conditions
	 */
	async findMany(
		options?: QueryOptions<TTable>,
	): Promise<InferSelectModel<TTable>[]> {
		let query = this.db.select().from(this.table);

		if (options?.where) {
			query = query.where(options.where);
		}

		if (options?.orderBy) {
			query = query.orderBy(options.orderBy);
		}

		if (options?.limit) {
			query = query.limit(options.limit);
		}

		if (options?.offset) {
			query = query.offset(options.offset);
		}

		return await query;
	}

	/**
	 * Get all records
	 */
	async findAll(): Promise<InferSelectModel<TTable>[]> {
		return await this.db.select().from(this.table);
	}

	/**
	 * Create a new record
	 */
	async create(
		data: InferInsertModel<TTable>,
	): Promise<InferSelectModel<TTable>> {
		const [created] = await this.db.insert(this.table).values(data).returning();
		return created;
	}

	/**
	 * Create multiple records
	 */
	async createMany(
		data: InferInsertModel<TTable>[],
	): Promise<InferSelectModel<TTable>[]> {
		return await this.db.insert(this.table).values(data).returning();
	}

	/**
	 * Update a record by ID
	 */
	async updateById(
		id: string,
		data: Partial<InferInsertModel<TTable>>,
	): Promise<InferSelectModel<TTable> | undefined> {
		const [updated] = await this.db
			.update(this.table)
			.set(data)
			// @ts-ignore - assume all tables have `id`
			.where(eq(this.table.id, id))
			.returning();
		return updated;
	}

	/**
	 * Update records matching conditions
	 */
	async updateMany(
		where: WhereCondition<TTable>,
		data: Partial<InferInsertModel<TTable>>,
	): Promise<InferSelectModel<TTable>[]> {
		let query = this.db.update(this.table).set(data);

		if (where) {
			query = query.where(where);
		}

		return await query.returning();
	}

	/**
	 * Delete a record by ID
	 */
	async deleteById(id: string): Promise<InferSelectModel<TTable> | undefined> {
		const [deleted] = await this.db
			.delete(this.table)
			// @ts-ignore - assume all tables have `id`
			.where(eq(this.table.id, id))
			.returning();
		return deleted;
	}

	/**
	 * Delete records matching conditions
	 */
	async deleteMany(
		where: WhereCondition<TTable>,
	): Promise<InferSelectModel<TTable>[]> {
		let query = this.db.delete(this.table);

		if (where) {
			query = query.where(where);
		}

		return await query.returning();
	}

	/**
	 * Count records matching conditions
	 */
	async count(where?: WhereCondition<TTable>): Promise<number> {
		let query = this.db.select({ count: count() }).from(this.table);

		if (where) {
			query = query.where(where);
		}

		const result = await query;
		return result[0]?.count || 0;
	}

	/**
	 * Paginate records
	 */
	async paginate(
		options: PaginationOptions & Omit<QueryOptions<TTable>, "limit" | "offset">,
	): Promise<PaginatedResult<InferSelectModel<TTable>>> {
		const page = options.page || 1;
		const perPage = options.perPage || 15;
		const offset = (page - 1) * perPage;

		// Get total count
		const total = await this.count(options.where);

		// Get paginated data
		const data = await this.findMany({
			...options,
			limit: perPage,
			offset: offset,
		});

		const totalPages = Math.ceil(total / perPage);

		return {
			data,
			pagination: {
				page,
				perPage,
				total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			},
		};
	}

	/**
	 * Check if record exists by ID
	 */
	async exists(id: string): Promise<boolean> {
		const record = await this.findById(id);
		return !!record;
	}

	/**
	 * Upsert (insert or update) a record
	 */
	async upsert(
		data: InferInsertModel<TTable>,
		conflictTarget?: string[],
	): Promise<InferSelectModel<TTable>> {
		// For PostgreSQL, we can use ON CONFLICT
		// This is a simplified version - you might need to adjust based on your needs
		try {
			return await this.create(data);
		} catch (error) {
			// If it's a unique constraint violation, try to update
			if (error instanceof Error && error.message.includes("unique")) {
				// @ts-ignore - assume all tables have `id`
				const existing = await this.findFirst({
					where: eq(this.table.id, (data as any).id),
				});
				if (existing) {
					// @ts-ignore
					return await this.updateById((data as any).id, data);
				}
			}
			throw error;
		}
	}

	/**
	 * Find or create a record
	 */
	async findOrCreate(
		where: WhereCondition<TTable>,
		defaults: InferInsertModel<TTable>,
	): Promise<{ record: InferSelectModel<TTable>; created: boolean }> {
		const existing = await this.findFirst({ where });

		if (existing) {
			return { record: existing, created: false };
		}

		const created = await this.create(defaults);
		return { record: created, created: true };
	}

	/**
	 * Refresh a record (reload from database)
	 */
	async refresh(id: string): Promise<InferSelectModel<TTable> | undefined> {
		return this.findById(id);
	}

	/**
	 * Execute a raw SQL query
	 */
	async raw<T = unknown>(query: string, params?: unknown[]): Promise<T[]> {
		return await this.db.execute(sql.raw(query, params));
	}

	/**
	 * Start a transaction
	 */
	async transaction<T>(
		callback: (tx: typeof this.db) => Promise<T>,
	): Promise<T> {
		return await this.db.transaction(callback);
	}

	// Query builder helpers

	/**
	 * Create WHERE conditions
	 */
	where(
		column: keyof InferSelectModel<TTable>,
		operator: string,
		value: any,
	): WhereCondition<TTable> {
		// @ts-ignore
		const col = this.table[column];

		switch (operator) {
			case "=":
				return eq(col, value);
			case "!=":
			case "<>":
				return ne(col, value);
			case ">":
				return gt(col, value);
			case ">=":
				return gte(col, value);
			case "<":
				return lt(col, value);
			case "<=":
				return lte(col, value);
			case "like":
				return like(col, value);
			case "ilike":
				return ilike(col, value);
			case "in":
				return inArray(col, value);
			case "not in":
				return notInArray(col, value);
			case "is null":
				return isNull(col);
			case "is not null":
				return isNotNull(col);
			default:
				throw new Error(`Unsupported operator: ${operator}`);
		}
	}

	/**
	 * Create ORDER BY conditions
	 */
	orderBy(
		column: keyof InferSelectModel<TTable>,
		direction: "asc" | "desc" = "asc",
	): OrderByCondition<TTable> {
		// @ts-ignore
		const col = this.table[column];
		return direction === "desc" ? desc(col) : asc(col);
	}

	/**
	 * Combine WHERE conditions with AND
	 */
	and(...conditions: WhereCondition<TTable>[]): WhereCondition<TTable> {
		const validConditions = conditions.filter(Boolean);
		return validConditions.length > 0 ? and(...validConditions) : undefined;
	}

	/**
	 * Combine WHERE conditions with OR
	 */
	or(...conditions: WhereCondition<TTable>[]): WhereCondition<TTable> {
		const validConditions = conditions.filter(Boolean);
		return validConditions.length > 0 ? or(...validConditions) : undefined;
	}
}
