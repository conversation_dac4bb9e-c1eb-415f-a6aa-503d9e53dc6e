import type {
  APGetCustomFieldType,
  GetAPAppointmentType,
  GetAPContactType,
} from "@libAP/APTypes";
import type {
  GetCCAppointmentType,
  GetCCCustomField,
  GetCCPatientType,
} from "@libCC/CCTypes";
import { relations } from "drizzle-orm";
import {
  customType,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

const customJsonb = <TData>(name: string) =>
  customType<{ data: TData; driverData: string }>({
    dataType() {
      return "jsonb";
    },
    toDriver(value: TData): string {
      return JSON.stringify(value);
    },
  })(name);

const commonColumns = {
  id: varchar("id", { length: 255 })
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
};

export const patient = pgTable("patients", {
  ...commonColumns,
  apId: varchar("ap_id", { length: 255 }).unique(),
  ccId: integer("cc_id").unique(),
  email: varchar("email", { length: 255 }),
  phone: varchar("phone", { length: 255 }),
  apUpdatedAt: timestamp("ap_updated_at"),
  ccUpdatedAt: timestamp("cc_updated_at"),
  apData: customJsonb<GetAPContactType>("ap_data"),
  ccData: customJsonb<GetCCPatientType>("cc_data"),
});

export const appointment = pgTable("appointments", {
  ...commonColumns,
  apId: varchar("ap_id", { length: 255 }).unique(),
  ccId: integer("cc_id").unique(),
  patientId: varchar("patient_id", { length: 255 }).references(
    () => patient.id
  ),
  apUpdatedAt: timestamp("ap_updated_at"),
  ccUpdatedAt: timestamp("cc_updated_at"),
  apData: customJsonb<GetAPAppointmentType>("ap_data"),
  ccData: customJsonb<GetCCAppointmentType>("cc_data"),
  apNoteID: text("ap_note_id"),
});

export const apCustomFields = pgTable("ap_custom_fields", {
  ...commonColumns,
  apId: varchar("ap_id", { length: 255 }).unique(),
  name: varchar("name", { length: 255 }).notNull(),
  config: customJsonb<APGetCustomFieldType>("config"),
});

export const ccCustomFields = pgTable("cc_custom_fields", {
  ...commonColumns,
  ccId: integer("cc_id").unique(),
  name: varchar("name", { length: 255 }).notNull(),
  config: customJsonb<GetCCCustomField>("config"),
});

export const errorLogs = pgTable("error_logs", {
  ...commonColumns,
  message: text("message").notNull(),
  stack: text("stack"),
  type: varchar("type", { length: 255 }).notNull(),
  data: jsonb("data"),
});

export const patientAppointmentsRelation = relations(patient, ({ many }) => ({
  appointments: many(appointment),
}));

export const appointmentPatientRelation = relations(appointment, ({ one }) => ({
  patient: one(patient, {
    fields: [appointment.patientId],
    references: [patient.id],
  }),
}));

const dbSchema = {
  patient,
  appointment,
  apCustomFields,
  ccCustomFields,
  errorLogs,
};
export default dbSchema;
